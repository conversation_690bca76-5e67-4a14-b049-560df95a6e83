"use client";

import { useCoinglassData } from "@/lib/state";
import { ConfigProvider, Table, TableProps } from "antd";
import { useState } from "react";
import FullscreenWrapper from "../comman/FullscreenWrapper";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";

// Time period options for funding rates
const timePeriodOptions = ["1d", "7d", "30d"];

export const FundingRatesTable = () => {
  const [timePeriod, setTimePeriod] = useState<string>("1d");
  const { data: fundingData, isLoading, error } = useCoinglassData('funding-rate');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Define the exchanges we want to show (based on your data structure)
  const exchanges = ['Binance', 'OKX', 'Bybit', 'KuCoin', 'dYdX', 'Bitget', 'CoinEx', 'Bitfinex', 'Kraken', 'HTX', 'BingX', 'Gate'];

  // Filter function based on time period - simulating different data for different periods
  const filterDataByTimePeriod = (data: any[], period: string) => {
    if (!data) return [];

    return data?.map((coin: any) => {
      const filteredCoin = { ...coin };

      // Simulate filtering by adjusting funding rates based on time period
      filteredCoin.stablecoin_margin_list = coin.stablecoin_margin_list?.map((exchange: any) => {
        let adjustedRate = exchange.funding_rate;

        // Apply different multipliers based on time period to simulate historical data
        switch (period) {
          case '1d':
            // Current rates (no change)
            adjustedRate = exchange.funding_rate;
            break;
          case '7d':
            // Simulate 7-day average (slightly lower)
            adjustedRate = exchange.funding_rate * 0.85;
            break;
          case '30d':
            // Simulate 30-day average (more conservative)
            adjustedRate = exchange.funding_rate * 0.75;
            break;
        }

        return {
          ...exchange,
          funding_rate: adjustedRate
        };
      });

      return filteredCoin;
    }) || [];
  };

  // Apply time period filter to the data
  const filteredFundingData = filterDataByTimePeriod(fundingData, timePeriod);

  // Transform data to match the funding rates table structure
  const tableData = filteredFundingData?.map((coin: any) => {
    const rowData: any = {
      key: coin.symbol,
      symbol: coin.symbol,
    };

    // Add funding rates for each exchange from stablecoin_margin_list
    exchanges.forEach((exchange) => {
      const exchangeData = coin.stablecoin_margin_list?.find((item: any) => item.exchange === exchange);
      rowData[exchange.toLowerCase().replace('.', '_')] = exchangeData?.funding_rate || null;
    });

    return rowData;
  }) || [];

  const fundingRatesColumns: TableProps<any>["columns"] = [
    {
      title: "Symbol",
      dataIndex: "symbol",
      key: "symbol",
      width: 100,
      align: "left",
      fixed: 'left',
      render: (text) => (
        <div className="flex items-center gap-2">
          <span className="font-normal text-text-primary">{text || '-'}</span>
        </div>
      ),
    },
    ...exchanges.map((exchange) => ({
      title: (
        <div className="flex flex-col items-center">
          <span className="text-xs">{exchange}</span>
          <span className="text-xs text-gray-400">({timePeriod})</span>
        </div>
      ),
      dataIndex: exchange.toLowerCase().replace('.', '_'),
      key: exchange.toLowerCase().replace('.', '_'),
      width: 90,
      align: "center" as const,
      render: (value: number) => {
        if (value === null || value === undefined) return '-';
        const percentage = value;
        const isPositive = percentage >= 0;
        const isNegative = percentage < 0;
        return (
          <span className={
            isPositive ? 'text-green-600' :
            isNegative ? 'text-red-600' :
            'text-gray-600'
          }>
            {percentage.toFixed(3)}%
          </span>
        );
      },
    })),
  ];

  if (error) {
    return (
      <div className="p-8">
        <p className="text-red-600">Error loading funding rates data</p>
      </div>
    );
  }

  return (
    <FullscreenWrapper title={`Funding Rates Table (${timePeriod.toUpperCase()})`}>
      <section className="mr-4">
        {/* Time Period Filter */}
        <div className="flex justify-end items-center mb-6">
          <div className="flex space-x-2">
            <ToggleGroup
              type="single"
              value={timePeriod}
              onValueChange={(value) => value && setTimePeriod(value)}
              className="flex rounded overflow-hidden border border-border-light px-2 py-1 gap-2 text-white"
            >
              {timePeriodOptions.map((option) => (
                <ToggleGroupItem
                  key={option}
                  value={option}
                  className={cn(
                    "outline-none border-none px-3 rounded last:rounded-tr last:rounded-br first:rounded-tl first:rounded-bl"
                  )}
                >
                  {option}
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          </div>
        </div>

        <ConfigProvider
          theme={{
            components: {
              Spin: { colorPrimary: "#bbd955" },
              Table: {
                fontFamily: "DM Sans",
                colorPrimary: "#bbd955",
                headerBg: "#1a1a1a",
                headerColor: "#ffffff",
                rowHoverBg: "#2d3748",
                colorBgContainer: "#222831",
                colorText: "#ffffff",
                colorTextHeading: "#ffffff",
                borderColor: "#404040",
                colorBorder: "#404040",
                bodySortBg: "#222831",
              },
            },
            token: {
              colorBgContainer: "#222831",
              colorText: "#ffffff",
              colorTextHeading: "#ffffff",
              colorBorder: "#404040",
            },
          }}
        >
          <Table
            columns={fundingRatesColumns}
            dataSource={tableData}
            rowKey="key"
            loading={isLoading}
            scroll={{ x: "1200px" }}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size || 10);
              },
              showSizeChanger: true,
              showQuickJumper: false,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
            }}
            className="crypto-table-dark"
          />
        </ConfigProvider>
        <div className="pb-12" />
      </section>
    </FullscreenWrapper>
  );
};
